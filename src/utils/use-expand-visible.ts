import { useEffect, useState } from 'react';
import ResizeObserver from 'resize-observer-polyfill';
  

  
// 检查文本是否超过指定行数
function isTextOverflow(element: HTMLElement, maxLine: number, title: string): boolean {
    // 参数校验
    if (!(element instanceof HTMLElement)) {
       return;
    }

    if (!maxLine || maxLine < 1) {
        return;
    }

    // // 确保元素已渲染到DOM中
    if (!element.isConnected) {
        return;
    }

    // 复制元素（深克隆）
    const clone = document.createElement('div') as HTMLElement;
    clone.innerHTML = title;
    // 获取原元素计算样式
    const computedStyle = getComputedStyle(element);

    // 重置所有可能影响行数的样式
    Object.assign(clone.style, {
        width: `${element.offsetWidth}px`, // 保持与原元素相同宽度
        height: 'auto', // 高度自适应内容
        dispaly: 'none',
        margin: '0', // 清除外边距干扰
        padding: computedStyle.padding, // 继承内边距
        font: computedStyle.font, // 继承字体相关样式
        lineHeight: computedStyle.lineHeight, // 继承行高
        boxSizing: computedStyle.boxSizing, // 继承盒模型
    });

    // 添加到DOM并强制重绘
    document.body.appendChild(clone);

    // 获取单行高度（克隆元素已被强制为单行）
    const singleLineHeight = clone.offsetHeight;

    // 移除克隆元素
    document.body.removeChild(clone);
    const actualLine = Math.ceil(singleLineHeight / 16); // 核心公式修复
    return actualLine > maxLine
}



/**
 * 自定义Hook：监听元素文本是否超过指定行数
 * @param {number} maxLine - 最大允许行数，默认3行
 * @returns {[React.RefObject, boolean]} - 返回元素引用和是否溢出的状态
 */
export function useTextOverflow({
    element,
    maxLine,
    content
}) {
    const [isOverflow, setIsOverflow] = useState(false);
    useEffect(() => {
        if (!element) return;

        // 检查文本是否溢出
        const checkOverflow = () => {
            setIsOverflow(isTextOverflow(element, maxLine, content));
        };

        // 创建尺寸观察器
        const resizeObserver = new ResizeObserver(() => {
            checkOverflow();
        });

        // 开始观察
        resizeObserver.observe(element);

        // 初始检查
        checkOverflow();

        // 清理函数：停止观察
        return () => {
            resizeObserver.disconnect();
        };
    }, [element, maxLine, content]);
    return isOverflow;
}

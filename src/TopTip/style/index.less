@class-prefix-toptip: ~'rbm-toptip';

.@{class-prefix-toptip} {
    background-color: white;
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    font-size: 12px;
    line-height: 16px;
    padding: 8px 12px;
    direction: ltr;
    &-rtl {
        direction: rtl;
        text-align: right;
        .@{class-prefix-toptip}-icon {
            margin-right: 0px;
            margin-left: 8px;
        }
        .@{class-prefix-toptip}-control {
            & > *:nth-child(1) {
                margin-left: 0px;
                margin-right: 8px;
            }
            & > *:nth-child(2) {
                margin-left: 0px;
                margin-right: 10px;
            }
        }
    }
    &-info {
        background-color: #fff8e1;
        color: #222;
    }
    &-warning {
        background-color: #fff1e7;
        color: #ff6a00;
        i {
            color: #ff6a00 !important;
        }
    }
    &-card {
        background: #f5f6fa;
        color: #222222;
        i {
            color: #222222 !important;
        }
    }
    &-success {
        background-color: rgba(0, 191, 127, 0.1);
        color: #222;
    }
    &-expanded-success {
        background-color: #D8E4E4;
        color: #222;
    }
    &-danger {
        background-color: #fff2f3;
        color: #222;
    }
    &-icon {
        flex: none;
        margin-right: 8px;
        font-size: 14px;
    }
    &-icon-size {
        font-size: 16px;
    }
    &-content-box {
        margin: 0px;
        flex: 1;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        position: relative;
    }
    &-expanded {
        position: absolute;
        bottom: 0;
        right: 0px;
        width: fit-content;
        height: fit-content;
        display: inline-block;
        box-shadow: -10px 0 3px 0 rgba(255, 255, 255, 0.2);
        line-height: 16px;
    }
    &-expanded-ellipsis {
        margin-left: 2px;
        display: inline-block;
        height: fit-content;
    }
    &-expanded-text {
        color: #FF6A00; 
        cursor: pointer;
        margin-left: 2px;
    }
    &-control {
        flex: none;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        flex-direction: row;
        & > *:nth-child(1) {
            margin-left: 8px;
        }
        & > *:nth-child(2) {
            margin-left: 10px;
        }
        &-icon {
            display: inline-block;
            height: 16px;
        }
    }
}

{"cname": "", "description": "", "tags": "", "author": "", "module": "es/index.js", "main": "lib/index.js", "unpkg": "dist/roo-b-mobile.umd.js", "types": "es/index.d.ts", "typings": "es/index.d.ts", "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": []}, "standard": true, "name": "@roo/roo-b-mobile", "version": "0.1.53", "buildOptions": {"buildType": "webpack", "framework": "react", "frameworkVersion": "16.14.0", "specialType": ["ts"], "alias": {}}, "_entry": "src/index.ts", "scripts": {"build": "npm run build:esm && npm run build:commonjs && npm run build:umd && npm run build:style && npm run build:typings", "build:esm": "rm -rf es && babel --no-babelrc --config-file ./.esm.babelrc src --out-dir es --extensions \".ts,.tsx\" --source-maps && tsc --declaration --emitDeclarationOnly", "build:commonjs": "rm -rf lib && babel --no-babelrc --config-file ./.babelrc src --out-dir lib --extensions \".ts,.tsx\" --source-maps", "build:umd": "rm -rf dist && webpack --config dist.config.js --mode=production", "build:style": "gulp", "build:typings": "rm -rf @types && tsc", "test": "npm run test:unit", "test:unit": "jest", "createDTs": "tsc", "lint": "lint-staged", "prepare": "husky install", "start": "roo runDemos", "peersDev": "npm-install-peers"}, "lint-staged": {"src/**/*.{ts,tsx,js,jsx}": ["pretty-quick --staged", "eslint", "git add"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "devDependencies": {"@babel/cli": "7.12.17", "@babel/core": "7.12.3", "@babel/plugin-transform-react-jsx": "7.14.5", "@babel/plugin-transform-runtime": "^7.15.8", "@babel/preset-env": "7.12.11", "@babel/preset-react": "^7.14.5", "@babel/preset-typescript": "7.14.5", "@testing-library/jest-dom": "^5.14.1", "@testing-library/react": "^12.1.5", "@testing-library/react-hooks": "^8.0.1", "@types/lodash": "^4.14.202", "@typescript-eslint/eslint-plugin": "^4.19.0", "@typescript-eslint/parser": "^4.19.0", "@wmfe/eslint-config-mt": "^0.5.9", "babel-eslint": "^10.1.0", "babel-loader": "8.2.2", "babel-polyfill": "6.26.0", "css-loader": "5.2.4", "es-check": "^7.2.1", "eslint": "^7.0.0", "eslint-config-airbnb": "^19.0.4", "eslint-import-resolver-webpack": "^0.13.8", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "file-loader": "6.2.0", "gulp": "4.0.2", "gulp-less": "5.0.0", "gulp-sass": "5.1.0", "html-webpack-plugin": "5.3.1", "husky": "^8.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-junit": "^16.0.0", "jest-transform-css": "^2.0.0", "less": "4.0.0", "less-loader": "^10.0.0", "lint-staged": "^11.0.3", "mini-css-extract-plugin": "1.6.0", "npm-install-peers": "^1.2.2", "postcss": "8.2.1", "postcss-cssnext": "3.1.0", "postcss-loader": "5.3.0", "prettier": "^2.8.7", "pretty-quick": "^3.1.3", "sass": "1.32.13", "sass-loader": "12.0.0", "style-loader": "2.0.0", "style-resources-loader": "1.5.0", "ts-jest": "^29.1.2", "ts-loader": "4.4.2", "tsc": "^2.0.3", "tslib": "2.2.0", "typescript": "^4.5.5", "url-loader": "4.1.1", "webpack": "5.40.0", "webpack-cli": "4.10.0", "webpack-dev-server": "3.11.2", "webpack-merge": "5.8.0"}, "dependencies": {"@babel/plugin-proposal-class-properties": "^7.18.6", "@floating-ui/dom": "^1.6.3", "@react-spring/web": "^9.7.3", "@use-gesture/react": "^10.3.0", "@utiljs/clone": "^0.2.8", "@utiljs/string": "^1.0.4", "@utiljs/type": "^0.5.5", "ahooks": "^3.7.10", "classnames": "^2.5.1", "core-js": "^3.35.1", "date-fns": "^3.6.0", "dayjs": "^1.11.10", "decimal.js": "^10.4.3", "lodash": "^4.17.21", "lorem-ipsum": "^2.0.8", "querystring-es3": "^0.2.1", "rc-cascader": "^3.26.0", "rc-field-form": "^2.0.0", "rc-tooltip": "^6.1.3", "rc-util": "^5.41.0", "react-fast-marquee": "^1.6.4", "react-is": "^18.2.0", "react-virtualized": "^9.22.5", "resize-observer-polyfill": "^1.5.1", "scroll-into-view-if-needed": "^2.2.31", "staged-components": "^1.1.3"}, "prevVersion": "0.0.1", "peerDependencies": {"ahooks": "^3.7.10", "classnames": "^2.5.1", "dayjs": "^1.11.10", "dom-helpers": "5.2.1", "lodash": "^4.17.21", "react": "^16.14.0", "react-dom": "^16.14.0"}, "sideEffects": ["dist/*", "*.css", "src/*.less"], "_isCreate": false}